const deviceservice = require('./device.service')
const componentUtils = require('../../utils/component/utils')
const flaverr = require('flaverr');

module.exports = {

  findOne: deviceservice.findOne,
  find: deviceservice.find,
  update: deviceservice.update,
  getParentControllerIdIfThirdPartyController: deviceservice.getParentControllerIdIfThirdPartyController,
  fetchMainMetersByScanningSiteConfiguration: deviceservice.fetchMainMetersByScanningSiteConfiguration,
  getDevicesBySiteId:deviceservice.getDevicesBySiteId,
  /**
   * @function getDeviceUpdatedCompData
   * @Object {string} deviceId :- deviceId
   {string} siteId
   {string} operation can have two values add or remove
   * @description Get device details with updated componentId of devices and adds/removes componentId based on the operation parameter
   * @returns {Object} Object:- return {
   deviceId,
   compData: getComponentIds,
   status: 'fullfilled',
   message: 'Device not found'
   }
   */
  getDeviceUpdatedCompData:  async function ({deviceId, siteId, updatedComponentId, operation}) {
    let device = await Devices.findOne({ deviceId, siteId })
    if (!device) return {
      deviceId,
      status: 'rejected',
      message: 'Device not found',
      operation
    };
    let {componentId: oldComponents } = device;
    const getComponentIds = componentUtils.generateComponentIds(
      oldComponents,
      updatedComponentId,
      operation
    )
    return {
      deviceId,
      compData: getComponentIds,
      status: 'fullfilled',
      message: 'Device found',
      operation
    }
  },
  fetchParameterListByDeviceIds:deviceservice.fetchParameterListByDeviceIds,
  findControllerIdByDeviceId: async function (deviceId){
    const deviceDetail = await Devices.findOne({deviceId});
    if (!deviceDetail) {
      throw flaverr({
          message: `device id ${deviceId} does not exist`,
          code: `E_DEVICE_NOT_FOUND`,
          operation: 'changeControlModeByCommand',
          data: {
            deviceId,
          },
        },
      )
    }
    const { controllerId } = deviceDetail;
    if(!controllerId){
      throw flaverr({
          message: `controller id is missing for this device-${deviceId}`,
          code: `E_CONTROLLER_NOT_ATTACHED_TO_DEVICE`,
          operation: 'changeControlModeByCommand',
          data: {
            deviceId,
          },
        },
      )
    }
    const controllerDetail = await Devices.findOne({deviceId:controllerId});
    if(!controllerDetail){
      throw flaverr({
          message: `controller id ${controllerId} does not exist`,
          code: `E_CONTROLLER_NOT_FOUND`,
          operation: 'changeControlModeByCommand',
          data: {
            deviceId,
            controllerId,
          },
        },
      )
    }

    //get siteJoulebox
    const siteJouleboxId = await deviceservice.getSiteJoulebox(controllerDetail);
    if (siteJouleboxId) {
      return {
        deviceId,
        controllerId: siteJouleboxId,
      };
    }

    let finalControllerId =
      (controllerDetail.hasOwnProperty("isSlaveController") &&
        controllerDetail.isSlaveController &&
        controllerDetail.hasOwnProperty("controllerId") &&
        controllerDetail.controllerId) ||
      controllerId;
    return {
      deviceId,
      controllerId:finalControllerId
    };
  },
  fetchParameterListByDeviceIds:deviceservice.fetchParameterListByDeviceIds,
  getDeviceCatBySiteId: deviceservice.getDeviceCatBySiteId,
  isDeviceExist: deviceservice.isDeviceExist,
  getDeviceListByDriverAndDeviceType: deviceservice.getDeviceListByDriverAndDeviceType,
  getDeviceList: deviceservice.getDeviceList,
  getDeviceControllerMap: deviceservice.getDeviceControllerMap,
  getControllerById: deviceservice.getControllerById,
};

/**
 *  @module DevicesController
 *  @description  Devices Controller module handles tasks related to devices.
 */
//ToDo : to get data of all devices of a particular site
// Instead of adding indexes to devicedata we are adding siteId as index of devices as count(Devices) << count (DeviceData)
const moment = require("moment-timezone");
const uuid = require("uuid/v4");
const DeviceService = require("../services/deviceService");
const siteService = require("../services/siteService");
const { getLinkedConfiguratorSystem } = require('../services/ConfiguratorHelper');
moment.tz.add(
  "Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");
let emitter = eventService.emmitter;
// let fs = require("fs");
// let util =  require("util");

module.exports = {
  "updateDashboardEMList": (req, res) => {
    let list = req.param("emList");
    if (
      !list ||
      !Array.isArray(list) ||
      list.length == 0 ||
      typeof req.param("siteId") === "undefined"
    ) {
      return res.badRequest({
        "err": "Insufficient Parameters",
        "data": null,
      });
    } else {
      DyanmoKeyStore.update(
        { "key": `${req.param("siteId")}_em` },
        { "list": list }
      )
        .then(function (data) {
          return res.ok({ "err": null, "status": "Done" });
        })
        .catch(err => {
          sails.log.error(new Error(err));
          return res.serverError({
            "err": "error updating",
            "data": null,
          });
        });
    }
  },
  /**
   * @name updateConsumptionPageEMList
   * @description Updates EM List configuration in user preference
   */
  "updateConsumptionPageEMList": async (req, res) => {
    try {
      let siteId = req._userMeta._site;
      let userId = req._userMeta.id;
      let { emList } = req.body;
      await UserSiteMap.update({ userId, siteId }, { "consumptionPageEMList": emList });
      return res.ok({
        "err": null,
        "status": "Done"
      });
    } catch (error) {
      sails.log.error("[updateConsumptionPageEMList] Server Error!", error);
      res.serverError({
        "err": "Error Updating",
        "data": null
      });
    }
  },
  "dashboardEMList": (req, res) => {
    if (!req.param("siteId"))
      return res.badRequest({ "err": "No parameters" });
    DyanmoKeyStore.find({ "key": `${req.param("siteId")}_em` })
      .then(function (data) {
        if (data.length == 0) {
          return res.ok({ "err": null, "data": [] });
        }
        return res.ok({ "err": null, "data": data[0].list });
      })
      .catch(err => {
        sails.log.error("error updating");
        return res.serverError({ "err": err, "data": null });
      });
  },
  /**
   * @name consumptionPageEMList
   * @description Fetches EM list for individual user based on configuration stored in UserSiteMap. If missing, fetches list stored in dyanmokeystore instead in key `${siteId}_em`
   */
  "consumptionPageEMList": async (req, res) => {
    try {
      let siteId = req._userMeta._site;
      let userId = req._userMeta.id;
      let emList, $userPreferenceUpdate;

      let userPreference = await UserSiteMap.findOne({ userId, siteId });
      // If configuration not present in user preferences, fetching site configuration and storing in user preferences.
      if (!userPreference.consumptionPageEMList) {
        emList = await DyanmoKeyStore.findOne({ "key": `${siteId}_em` });
        emList = emList.list;
        $userPreferenceUpdate = UserSiteMap.update({ userId, siteId }, { "consumptionPageEMList": emList });
      } else {
        emList = userPreference.consumptionPageEMList;
      }
      res.ok({
        "err": null,
        "data": emList
      });
      // Logging just incase error while updating user preference.
      try {
        await $userPreferenceUpdate;
      } catch (error) {
        sails.log.error(`[consumptionPageEMList] Error updating Consumption Page EM list in user preference for user: ${userId} in siteId: ${siteId}`);
      }
    } catch (error) {
      sails.log.error("[consumptionPageEMList] Server Error!", error);
      return res.serverError();
    }
  },
  // MODES APIs

  // PID RELATED STUFF
  "setPidConfig": (req, res) => {
    // req.body = {
    //      "config" : {
    //       "test":"test"
    //      },
    //      "controllerId" : 127,
    //      "componentId" : "smt-del_7",
    //      "siteId" : "smt-del",
    // };

    try {
      let controllerId = req.body.controllerId;
      let componentId = req.body.componentId;
      let config = req.body.config;
      let siteId = req.body.siteId;
      let userId = req._userMeta["id"];
      if (!siteId || !config || !componentId || !controllerId) {
        res.badRequest("No data recieved");
        return res.end();
      }

      sails.log("Broadcasting command AHU setPoint Pid");

      let upObj = {
        "config": config,
        "extra": {
          siteId,
          componentId,
          "controlId": controllerId,
          "userId": userId,
        },
        "operation": "pidConfig",
        "ts": moment().unix() * 1000,
      };
      // sails.sockets.broadcast(siteId, "pidCommand", { data,componentId ,controllerId });
      let stopic = `${siteId}/config/${controllerId}/pid`;
      eventService.publish(stopic, JSON.stringify(upObj));
      //  || make a request to firmware /config/controllerId/pid
      res.send();
    } catch (e) {
      sails.log(e);
      res.badRequest();
    }
  },
  "pidList": function (req, res) {
    // req.body ={
    //     siteId : 'smt-del'
    // }

    try {
      let siteId = req.body.siteId;
      if (!siteId) {
        return res.badRequest("no siteId Params");
      }
      Devices.find({ "siteId": siteId })
        .then(d => {
          let t = [];
          if (d) {
            d.forEach(dev => {
              if (!dev.controllerId) {
                if (
                  !(
                    dev.pidOn &&
                    dev.pid &&
                    helper.toJson(dev.pid)
                  )
                ) {
                  t.push(dev);
                }
              }
            });
          }
          res.send(t);
        })
        .catch(e => {
          sails.log.error(e);
          return res.badRequest();
        });
    } catch (e) {
      sails.log.error(e);
      return res.badRequest();
    }
  },
  "getPidConfig": function (req, res) {
    try {
      let componentId = req.body.componentId;
      if (!componentId) {
        return res.badRequest("Param error");
      }
      Component.findOne({ "deviceId": componentId })
        .then(controller => {
          if (controller && controller.pidOn) {
            return Devices.findOne({
              "deviceId": controller.pidOn,
            });
          } else {
            false;
          }
        })
        .then(d => {
          if (d) {
            res.send(d);
          } else {
            res.send();
          }
        });
    } catch (e) {
      sails.log.error(e);
      return res.badRequest();
    }
  },
  "setMode": async function (req, res) {
    /**
     * @param req.body.did
     * @param req.body.siteId
     * @param req.body.controllerId
     * When there is a mode change, this API is called for all devices of a controller
     * one at a time. Meaning if the controller has 3 devices, this API will be called
     * 3 times.
     *
     * In case the controllerId belongs to a 3rd party controller like, Masibus, it's parent
     * controllerId is sent.
     *
     */
    try {
      let { siteId, did, controllerId } = req.body;
      const transactionId = req?.headers?.["x-transaction-id"];
      let userId = req._userMeta["id"];
      let deviceIdArray = helper.toJson(did);

      if (!siteId || !deviceIdArray || !userId || !controllerId) {
        res.badRequest("invalid");
        return res.end();
      }

      sails.log.info(`level="INFO" app="controller-feedback" operation="modeChange" step="1/8" traceId="${transactionId}" requestId="${transactionId}" userId="${userId}" siteId="${siteId}" modeChangeDetail="${JSON.stringify(deviceIdArray)}" controllerId="${controllerId}" message="Mode Change request initiated" state="in-progress"`);

      for (let deviceDotParam in deviceIdArray) {
        if (!(deviceDotParam.split(".").length == 2)) {
          res.badRequest();
          return res.end();
        }
      }

      /**Modifying API to accommodate for 3rd party controllers*/
      let verifiedControllerId = await checkControllerId(controllerId);

      let upObj = {
        "operation": "InsertInModes",
        "traceId": transactionId,
        "requestId": uuid(),
        "extra": { controllerId: verifiedControllerId, siteId },
        "config": {
          ...deviceIdArray,
        },
      };
      /**make a request config/controllerId/mode with data deviceIdArray*/
      let stopic = `${siteId}/config/${verifiedControllerId}/mode`;
      eventService.publish(stopic, JSON.stringify(upObj));
      sails.log.info(`level="INFO" app="controller-feedback" operation="modeChange" step="2/8" traceId="${transactionId}" requestId="${upObj.requestId}" message="Mode Change request published to IoT Core" state="in-progress" siteId=${siteId} controllerId=${controllerId} topic=${stopic} payload=${JSON.stringify(upObj)}`);
      sails.log.info(`level="INFO" app="controller-feedback" operation="modeChange" step="3/8" traceId="${transactionId}" requestId="${transactionId}" message="Mode change request placed successfully" state="in-progress" userId="${userId}" siteId="${siteId}"`);
      return res.ok({ "error": false });

      // Helper functions
      async function checkControllerId(controllerId) {
        try {
          let controllerConfiguration = await Devices.findOne({
            deviceId: controllerId
          });
          if (controllerConfiguration["isSlaveController"] === undefined)
            return controllerId; // If key is not present, it is not a 3rd party controller as old configurations do not have this key.
          if (controllerConfiguration["isSlaveController"] === true)
            return controllerConfiguration["controllerId"]; // This is the parent/master controllerId on which the topic needs to be broadcasted.
        } catch (error) {
          sails.log.error("Error either fetching device configuration or while searching for key. Returning old controllerId.");
          return controllerId;
        }
      }
    } catch (e) {
      sails.log(e);
      res.badRequest();
      return res.end();
    }
  },

  // END OF PID RELATED STUFF

  // New API's start for device config
  //Will add a new device to dejoule
  /**
   * @function addDevice
   * @param req
   * @param req.body.deviceInfo Required parameter, contains all the properties of the device that is to be added.
   * @param deviceInfo.networkId required
   * @param deviceInfo.regionId required
   * @param deviceInfo.portNumber required
   * @param deviceInfo.controllerId required
   * @param deviceInfo.communicationType required
   * @param deviceInfo.communicationCategory required
   * @param deviceInfo.driverType required
   * @param deviceInfo.deviceType required
   * @param deviceInfo.areaId required
   * @param deviceInfo.name required
   * @param deviceInfo.siteId required
   * @param deviceInfo.isVirtualDevice
   * @param res Response object
   * @description Adds the device configuration to device table and trigger corresponding hooks for
   * a) Updating global timestamp and total device count in DynamoKeyStoreTable
   * b) Adding parameters of the the device in paramters table
   * c) Triggers event to log the updates.
   * d) Emits a socket event for the update to frontend.
   * @returns 400 status code if any of the required request parameters are not present.
   * @returns 500 in case of server error
   * @return 200 status code with newly created device object.
   *
   */
  "addDevice": async (req, res) => {
    let deviceConfig = req.param("deviceInfo");
    filterKeys(deviceConfig);
    removeNullValues(deviceConfig);
    try {
      let siteInfoPromise = siteService.getSiteInfo(deviceConfig.siteId);
      let devicesListPromise = Devices.find({
        "siteId": deviceConfig.siteId,
      });
      let deviceIdCount = await DyanmoKeyStore.findOne({
        "key": "totalDeviceCount",
      });
      if (!deviceIdCount) {
        //For new site, to add first device
        deviceIdCount = { "value": 0 };
      }
      let deviceId = Number(deviceIdCount.value) + 1;
      deviceConfig.deviceId = deviceId;
      if (
        deviceConfig.communicationCategory == "AVO" ||
        deviceConfig.communicationCategory == "DR" ||
        deviceConfig.communicationCategory == "MasibusDO"
      )
        deviceConfig.functionType = "write";
      else if (
        deviceConfig.communicationCategory == "VFD" ||
        deviceConfig.communicationCategory == "CHL" ||
        deviceConfig.communicationCategory == "HPC"
      )
        deviceConfig.functionType = "both";
      else deviceConfig.functionType = "read";
      //TODO : verify component types and remove from else
      let siteInfo = await siteInfoPromise;
      //check: if regions and area mentioned is valid
      if (
        !siteInfo.areas[deviceConfig.areaId] ||
        !siteInfo.regions[deviceConfig.regionId] ||
        !siteInfo.networks[deviceConfig.networkId]
      ) {
        return res.badRequest({
          "err": "Invalid values for regions and areas",
        });
      }
      let deviceInfo = await devicesListPromise;
      if (deviceConfig.communicationType === "MB") {
        let devicesList = deviceInfo;
        let controllerId = deviceConfig.controllerId;
        let { availableIndexes: availableSlaveIds } = await deviceService.getAvailableSlaveIds(
          { controllerId, devicesList }
        );
        if (availableSlaveIds.length == 0) {
          return res.badRequest({
            "err": "No free slave Id available",
          });
        }
        if (
          deviceConfig.slaveId &&
          availableSlaveIds.indexOf(deviceConfig.slaveId) == -1
        ) {
          //Given slave Id does exists.
          return res.badRequest({
            "err": "Slave Id exists",
            "availableSlaves": availableSlaveIds,
          });
        } else if (!deviceConfig.slaveId) {
          deviceConfig.slaveId = availableSlaveIds[0];
        }
      } else {
        deviceConfig.slaveId = "0";
      }
      if (deviceConfig.hasOwnProperty('isVirtualDevice')) {
        deviceConfig.isVirtualDevice = deviceConfig["isVirtualDevice"];
      } else {
        deviceConfig.isVirtualDevice = "0"
      }
      emitter.emit("initChange", req);
      DyanmoKeyStore.update(
        { "key": "totalDeviceCount" },
        { "value": deviceId }
      )
        .then(count => Devices.create(deviceConfig))
        .then(async d => {
          emitter.emit("commitChange", req, "devices", [], d);
          emitter.emit("pushChange", req);
          let eventData = {
            "event": "create",
            "data": [deviceConfig],
          };
          eventService.notifyJouleTrack(
            deviceConfig.siteId,
            "public",
            "devices",
            eventData
          );
          let siteId = deviceConfig.siteId;
          await deviceService.updateConfigTs({ siteId });
          await parametersService.addDeviceParameters(
            deviceConfig.deviceType,
            deviceConfig.driverType,
            deviceConfig.deviceId,
            deviceConfig.siteId
          );
          return res.ok(d);
        })
        .catch(e => {
          emitter.emit("stashChange", req);
          sails.log.error(e);
          return res.serverError();
        });
    } catch (e) {
      sails.log.error(e);
      return res.serverError();
    }

    /******* Helpers */
    function filterKeys(deviceConfig) {
      if (
        !deviceConfig ||
        !deviceConfig.networkId ||
        !deviceConfig.regionId ||
        !deviceConfig.portNumber ||
        !deviceConfig.controllerId ||
        !deviceConfig.communicationType ||
        !deviceConfig.communicationCategory ||
        !deviceConfig.driverType ||
        !deviceConfig.deviceType ||
        !deviceConfig.areaId ||
        !deviceConfig.name ||
        !deviceConfig.siteId
      ) {
        return res.badRequest({ "err": "Insufficient Parameters" });
      }
    }
    function removeNullValues(deviceConfig) {
      Object.keys(deviceConfig).forEach(e => {
        if (
          typeof deviceConfig[e] === "undefined" ||
          deviceConfig[e] === null
        )
          delete deviceConfig[e];
      });
    }
  },
  "addController": async (req, res) => {
    let cntrlConfig = req.param("deviceInfo");
    let cntrlArr = [];
    //BASIC FILTERING
    if (Array.isArray(cntrlConfig)) {
      cntrlArr = cntrlConfig;
    } else if (typeof cntrlConfig == "object") {
      cntrlArr.push(cntrlConfig);
    } else {
      return res.badRequest();
    }



    let mapping = {
      "jouleiocontrol": "JouleIOControl-",
      "joulelogger": "JouleLogger-",
      "joulestat": "JouleStat-",
      "joulemomo": "JouleMomo-",
      "joulebox": "JouleBox-",
      "jouleleaf": "JouleLeaf-",
      "masibusdo16": "MasibusDO16-",
      "masibusdi16": "MasibusDI16-",
      "renuai8": "RenuAI8-",
      "renuaiao6": "RenuAIAO6-",
      "renudido14": "RenuDIDO14-",
      "renudido16": "RenuDIDO16-",
      "masibusai8": "MasibusAI8-",
      "masibusao8": "MasibusAO8-",
      "jouleleafext1": "JouleLeafExtension1-",
      "jouleleafext2": "JouleLeafExtension2-",
      "n3uronbacnetmqtt": "N3uronBACnetMQTT-",
      "n3uronbacnetmqtt-controller": "VirtualN3uronBACnetMQTT-",
    };
    let vendorList = ["smartjoules", "masibus", "renu", "n3uron", "n3uronbacnetmqtt"];
    const THIRD_PARTY_BMS_SLAVE_CONTROLLERS = ['n3uron'];

    let isBadRequest = false;
    cntrlArr.map(deviceConfig => {
      if (
        !deviceConfig ||
        !deviceConfig.networkId ||
        !deviceConfig.regionId ||
        !deviceConfig.softwareVer ||
        !deviceConfig.hardwareVer ||
        !deviceConfig.vendorId ||
        !deviceConfig.operationMode ||
        !deviceConfig.deviceType ||
        !deviceConfig.areaId ||
        !deviceConfig.siteId
      ) {
        isBadRequest = true;
      }
      let { deviceType, vendorId } = deviceConfig;
      if (
        deviceType != "joulesense" &&
        !mapping[deviceConfig.deviceType] &&
        vendorId == "smartjoules"
      ) {
        isBadRequest = true;
      }
      if (
        (deviceType == "joulesense" || vendorList.indexOf(vendorId) == -1) &&
        !deviceConfig.name
      ) {
        isBadRequest = true;
      }
    });
    if (isBadRequest) {
      return res.badRequest({ "err": "Insufficient Parameters" });
    }
    let siteId = cntrlArr?.[0]?.["siteId"] || req?._userMeta?._site;

    let resObj = {
      "success": [],
      "err": [],
    };
    //FILTERING ends

    try {
      let siteInfo = await siteService.getSiteInfo(siteId);

      if (!siteInfo) {
        return res.badRequest({ "err": "No such site exists" });
      }
      emitter.emit("initChange", req);
      let devicesList = await Devices.find({ "siteId": siteId });
      //To get max count of controllers added to site

      let devTypeCountMap = {
        "jouleiocontrol": 0,
        "joulelogger": 0,
        "joulestat": 0,
        "joulemomo": 0,
        "joulebox": 0,
        "jouleleaf": 0,
        "masibusdo16": 0,
        "masibusdi16": 0,
        "renuai8": 0,
        "renuaiao6": 0,
        "renudido14": 0,
        "renudido16": 0,
        "n3uronbacnetmqtt": 0,
        "n3uronbacnetmqtt-controller": 0
      };

      devicesList.map(e => {
        try {
          if (mapping[e.deviceType]) {
            if (!devTypeCountMap[e.deviceType]) {
              devTypeCountMap[e.deviceType] = 0;
            }
            let nameId = e?.name && parseInt(e?.name.split("-")[1]);
            if (Number.isNaN(nameId)) {
              sails.log.error(`Device ${e.deviceId} has invalid name format. Expected format: <deviceType>-<number>. Skipping nameId extraction.`);
              return
            }
            devTypeCountMap[e.deviceType] =
              nameId > devTypeCountMap[e.deviceType]
                ? nameId
                : devTypeCountMap[e.deviceType];
          }
        } catch (err) {
          sails.log.error(err);
        }
      });

      let { areas, networks, regions } = siteInfo;
      let isSystemIbms = false;
      let updatedDevices = [];
      for (let index in cntrlArr) {
        let deviceConfig = cntrlArr[index];
        if (deviceConfig.isSlaveController && deviceConfig.controllerId && THIRD_PARTY_BMS_SLAVE_CONTROLLERS.includes(deviceConfig.vendorId)) {
          const result = await DeviceService.validateBMSXSlaveControllerAttachment(deviceConfig)
          if (!result.success) {
            return res.badRequest({
              "err": result.message || "Device is not eligible for BMSX slave attachment."
            });
          }
        }

        // Check if trying to attach slave controller to a third-party virtual master controller (BMSX)
        if (deviceConfig.controllerId) {
          const bmsxService = require('../services/bmsx/bmsx.service');
          const isThirdPartyVirtualMaster = await bmsxService.isThirdPartyVirtualMasterController(deviceConfig.controllerId, siteId);
          if (isThirdPartyVirtualMaster) {
            return res.status(422).json({
              "err": "Cannot attach slave controllers to third-party virtual master controllers (BMSX). Direct slave controller attachment is not allowed for these controllers."
            });
          }
        }
        let deviceIdCount = await DyanmoKeyStore.findOne({
          "key": "totalDeviceCount",
        });
        if (!deviceIdCount) {
          //For new dejoule setup, to add first device | genrally will be used for development
          deviceIdCount = { "value": 0 };
        }
        let deviceId = Number(deviceIdCount.value) + 1;
        deviceConfig.deviceId = String(deviceId);
        if (mapping[deviceConfig.deviceType]) {
          if (devTypeCountMap.hasOwnProperty(deviceConfig.deviceType)) {
            devTypeCountMap[deviceConfig.deviceType] += 1;
          } else devTypeCountMap[deviceConfig.deviceType] = 1;
          if (deviceConfig.deviceType === 'renudido16') {
            deviceConfig.name =
              mapping[deviceConfig.deviceType] +
              (devTypeCountMap[deviceConfig.deviceType] > devTypeCountMap['renudido14'] ?
                devTypeCountMap[deviceConfig.deviceType] :
                devTypeCountMap['renudido14'] + 1);
          }
          else {
            deviceConfig.name =
              mapping[deviceConfig.deviceType] +
              devTypeCountMap[deviceConfig.deviceType];
          }
        }
        if (deviceConfig.regionId != "ibms" || deviceConfig.areaId != "ibms") {
          if (
            !regions ||
            !areas ||
            !networks ||
            !regions[deviceConfig.regionId] ||
            !areas[deviceConfig.areaId] ||
            !networks[deviceConfig.networkId]
          ) {
            resObj.err.push(deviceConfig);
            sails.log.error(
              deviceConfig,
              "invalid values of regions and areas"
            );
            continue;
          }
        } else {
          isSystemIbms = true;
        }


        // Verifying / Appending slave Id if "controllerId" key present in case of child controller.
        if (deviceConfig.controllerId) {
          let slaveArray = new Array(255), // Possible Modbus slave Ids are from 0 - 255
            availableIndexes = [];
          let { controllerId } = deviceConfig;
          slaveArray.fill(false);
          devicesList.forEach(device => {
            if (device.controllerId == controllerId) slaveArray[device.slaveId] = true;
          });
          slaveArray.forEach((availability, index) => {
            if (!availability) availableIndexes.push(index.toString());
          });
          if (availableIndexes.length == 0) {
            resObj.err.push(deviceConfig);
            deviceConfig["error"] = "No free slave Id available";
            sails.log.error(deviceConfig, "No free slave Id available");
            continue;
          }
          if (deviceConfig.slaveId && availableIndexes.indexOf(deviceConfig.slaveId) == -1) {
            resObj.err.push(deviceConfig);
            deviceConfig["error"] = "Slave Id Exists!";
            sails.log.error(deviceConfig, "Slave Id Exists!");
            continue;
          } else if (!deviceConfig.slaveId) {
            deviceConfig.slaveId = availableIndexes[0];
          }
        }

        try {
          await DyanmoKeyStore.update(
            { "key": "totalDeviceCount" },
            { "value": deviceId }
          );
          if (deviceConfig.deviceType === "joulebox") {
            DeviceService.markPrimaryAndSecondaryJouleBox(deviceConfig, devicesList.filter(device => device.deviceType === "joulebox"));
          }
          let newDevice = await Devices.create(deviceConfig);
          if (DeviceService.isBMSXSlaveController(deviceConfig)) {
            await DeviceService.linkBMSXSlaveControllerWithBMSXDatabase(deviceConfig)
          }

          updatedDevices.push(newDevice);

          let eventData = {
            "event": "create",
            "data": [deviceConfig],
          };
          eventService.notifyJouleTrack(
            deviceConfig.siteId,
            "public",
            "devices",
            eventData
          );
          resObj.success.push(newDevice);
        } catch (e) {
          emitter.emit("stash", req);
          sails.log.error(e);
          resObj.err.push(deviceConfig);
        }
      }
      emitter.emit("commitChange", req, "devices", [], updatedDevices);

      if (!isSystemIbms) {
        //Add controllers to site configuration and update it.
        for (let index in resObj.success) {
          let deviceConfig = resObj.success[index];
          regions[deviceConfig.regionId].controller.push(
            deviceConfig.deviceId
          );
          networks[deviceConfig.networkId].push(deviceConfig.deviceId);
        }
        let updatedSite = await Sites.update(
          { "siteId": siteId },
          { regions, areas }
        );
        let siteEventData = {
          "event": "update",
          "data": updatedSite,
        };
        eventService.notifyJouleTrack(
          siteId,
          "public",
          "sites",
          siteEventData
        );
        emitter.emit("commitChange", req, "sites", siteInfo, updatedSite);
        emitter.emit("pushChange", req);
      }

      await deviceService.updateConfigTs({ siteId });
      return res.ok(resObj);
    } catch (e) {
      emitter.emit("stash", req);
      sails.log.error(e);
      return res.serverError(e);
    }
  },
  //to be tested
  "editController": async (req, res) => {
    let { deviceConfig } = req.allParams();
    if (!deviceConfig || !deviceConfig.deviceId) return res.badRequest();
    try {
      let device = await Devices.findOne({
        "deviceId": deviceConfig.deviceId,
      });

      //basic filtering
      if (!device)
        return res.badRequest({
          "err": "Controller not available in our records",
        });
      if (device.deviceType != "joulesense" && deviceConfig.name) {
        return res.badRequest({
          "err": "Cannot change name of " + device.deviceType,
        });
      }

      emitter.emit("initChange", req);

      let keyObj = {
        "deviceId": device.deviceId,
        "siteId": device.siteId,
      };
      let { areaId, regionId, slaveId } = deviceConfig;

      let siteStatus;

      if (regionId || areaId) {
        //update the controller details in site schema
        siteStatus = await siteService.updateControllerInRegions(
          device,
          { req, regionId, areaId }
        );
        if (siteStatus.err) {
          return res.badRequest({ "err": siteStatus.err });
        }
      }

      if (slaveId) {
        let devicesList = await Devices.find({ "siteId": device.siteId });
        let slaveArray = new Array(255), // Possible Modbus slave Ids are from 0 - 255
          availableIndexes = [];
        let { controllerId } = device;
        slaveArray.fill(false);
        devicesList.forEach(device => {
          if (device.controllerId == controllerId) slaveArray[device.slaveId] = true;
        });
        slaveArray.forEach((availability, index) => {
          if (!availability) availableIndexes.push(index.toString());
        });
        if (availableIndexes.indexOf(slaveId) == -1) {
          return res.badRequest({
            "err": "Slave ID in use. Can not update controller."
          });
        }
      }

      //TODO better filtering of deviceConfig
      //saveing the changes to db
      let newD = await Devices.update(keyObj, deviceConfig);
      emitter.emit("commitChange", req, "devices", device, newD);
      let eventData = {
        "event": "update",
        "data": newD[0],
      };
      if (siteStatus) {
        eventService.notifyJouleTrack(
          device.siteId,
          "public",
          "sites",
          siteStatus
        );
      }
      let siteId = device.siteId;
      eventService.notifyJouleTrack(
        siteId,
        "public",
        "devices",
        eventData
      );
      emitter.emit("pushChange", req);
      //updating site configuration timestamp which is used by controller to keep their schema up to date.
      await deviceService.updateConfigTs({ siteId });
      return res.ok({
        "status": "done",
      });
    } catch (err) {
      emitter.emit("stashChange", req);
      sails.log.error(err);
      return res.serverError({ "err": "Internal Server Error" });
    }
  },
  /**
   * @param {object} req
   * @param {object} res
   * @description Edits a device and it's properties and updates in database
   */
  "editDevice": async (req, res) => {
    let { deviceConfig } = req.allParams();
    if (!deviceConfig || !deviceConfig.deviceId) return res.badRequest();
    try {
      let device = await Devices.findOne({
        "deviceId": deviceConfig.deviceId,
      });
      let { driverType } = deviceConfig;
      let { siteId, deviceId, communicationType } = device;
      emitter.emit("initChange", req);
      //basic filtering
      if (!device)
        return res.badRequest({
          "err": "Controller not available in our records",
        });
      let { slaveId, isMainMeter } = deviceConfig;
      if (slaveId && communicationType != "MBIP") { // Not relevant for MBIP devices as their slaveIds are of the format `IP:PORT` instead of numerical values.
        //check if slave id is available
        let { controllerId, siteId } = device;
        let { availableIndexes: availableSlaves, slaveIdToDeviceTypeMap } = await deviceService.getAvailableSlaveIds({
          controllerId,
          siteId,
        });
        let slaveSet = new Set(availableSlaves);
        if (!slaveSet.has(slaveId.toString())) {
          if (device.deviceType != "vrfController" && slaveIdToDeviceTypeMap[slaveId] != "vrfController") {
            // Hardcoding check for vrfController as it's the only exception which allows other vrfControllers
            // to be configured on the same slaveId
            return res.badRequest({ "err": "Slave Id already in use" });
          }
        }
      }
      let keyObj = {
        "deviceId": deviceId,
        "siteId": siteId,
      };
      //hook for setting main meters
      if (typeof isMainMeter != "undefined") {
        if (
          isMainMeter.toString() !== "true" &&
          isMainMeter.toString() !== "false"
        ) {
          return res.badRequest({
            "err": "isMainMeter should be a boolean",
          });
        } else {
          let status = await updateMainMeterList(
            isMainMeter.toString(),
            deviceId,
            siteId
          );
          if (!status)
            return res.badRequest({
              "err": "Unable to update mappings in key store",
            });
        }
      }
      if (
        typeof driverType !== "undefined" &&
        driverType !== device.driverType
      ) {
        await parametersService.addDeviceParameters(
          device.deviceType,
          driverType,
          device.deviceId,
          device.siteId
        );
      }
      //TODO: do better filtering
      delete deviceConfig.deviceId;
      delete deviceConfig.siteId;

      for (let dev in deviceConfig) {
        if (!deviceConfig[dev]) delete deviceConfig[dev];
      }
      let updatedDevice = await Devices.update(keyObj, deviceConfig);
      emitter.emit("commitChange", req, "devices", device, updatedDevice);
      let eventData = {
        "event": "update",
        "data": updatedDevice[0],
      };
      eventService.notifyJouleTrack(
        device.siteId,
        "public",
        "devices",
        eventData
      );
      emitter.emit("pushChange", req);

      await deviceService.updateConfigTs({ siteId });
      return res.ok({ "status": "done" });
    } catch (e) {
      emitter.emit("stashChange", req);
      sails.log.error(e);
      return res.serverError({ "err": "Internal Server Error!" });
    }
    /* Helpers */
    /**
     *
     * @param {string} isMainMeter true or false string
     * @param {string} deviceId deviceId of energymeter
     * @param {string} siteId site in which it should be reflected
     * @desciption appends or removes the energymeter id based on isMainmeter flag
     * @return {boolean} status true if inserted successfully or false otherwise
     *
     */
    async function updateMainMeterList(isMainMeter, deviceId, siteId) {
      let key = `${siteId}_mainMeter`;
      let value;
      let mainMeters = await DyanmoKeyStore.findOne({ key });
      if (!mainMeters) mainMeters = { "value": "" };
      if (isMainMeter === "false" || isMainMeter === false) {
        let meterlist = mainMeters.value;
        let meters = new Set(meterlist.split(","));
        meters.delete(deviceId);
        value = [...meters].join(",");
      } else if (isMainMeter === "true" || isMainMeter === true) {
        let meters = mainMeters.value;
        meters = meters ? `${meters},${deviceId}` : `${deviceId}`;
        let uniqueMeters = new Set(meters.split(","));
        value = [...uniqueMeters].join(",");
      }
      try {
        if (value.length !== 0)
          await DyanmoKeyStore.update({ key }, { value });
        else await DyanmoKeyStore.destroy({ key });
        return true;
      } catch (e) {
        sails.log.error(e);
        return false;
      }
    }
  },
  "deleteController": async (req, res) => {
    let { deviceId, siteId, industryType } = req.allParams();
    let resObj = {
      "deleted": [],
      "notDeleted": [],
      "err": [],
    };
    try {
      let allDevices = await Devices.find({ "siteId": siteId });
      let controller = {};
      let contrlDevs = allDevices.filter(dev => {
        if (dev.deviceId == deviceId) controller = dev;
        return dev.controllerId == deviceId;
      });
      if (Object.keys(controller).length == 0)
        return res.badRequest({ "err": "Not a controller" });
      emitter.emit("initChange", req);
      let logData = [];
      for (let i in contrlDevs) {
        try {
          let device = contrlDevs[i];
          let { deviceId } = device;
          if (device.componentId && device.componentId != "NA") {
            resObj.notDeleted.push(deviceId);
            resObj.err.push(
              `DeviceId ${deviceId} is a part of component with componentId: ${device.componentId}`
            );
            continue;
          }
          siteId = device.siteId;
          sails.log("Deleted ", device);
          await Devices.destroy({
            "deviceId": deviceId,
            "siteId": siteId,
          });
          logData.push(device);
          resObj.deleted.push(deviceId);
        } catch (e) {
          resObj.notDeleted.push(deviceId);
          sails.log.error(e);
        }
      }

      //now till here all connected devices should be deleted
      let siteInfo;
      if (contrlDevs.length == resObj.deleted.length) {
        //delete controller
        await Devices.destroy({
          "deviceId": deviceId,
          "siteId": siteId,
        });
        resObj.deleted.push(deviceId);
        logData.push(controller);
        if (industryType != "ibms")
          siteInfo = await siteService.removeControllerFromRegion({
            req,
            ...controller,
          });
        //remove it from region
      }
      emitter.emit("commitChange", req, "devices", logData, []);
      let eventData = {
        "event": "delete",
        "data": resObj.deleted,
      };
      eventService.notifyJouleTrack(
        siteId,
        "public",
        "devices",
        eventData
      );
      if (industryType != "ibms")
        eventService.notifyJouleTrack(siteId, "public", "sites", siteInfo);

      emitter.emit("pushChange", req);
      await deviceService.updateConfigTs({ siteId });
      return res.ok(resObj);
    } catch (e) {
      let eventData = {
        "event": "delete",
        "data": resObj.deleted,
      };
      eventService.notifyJouleTrack(
        siteId,
        "public",
        "devices",
        eventData
      );
      sails.log.error(e);
      return res.serverError(resObj);
    }
    //devices
  },
  "deleteDevice": async (req, res) => {
    let deviceIds = req.param("deviceId");
    let deviceList = [];
    let resObj = {
      "deleted": [],
      "notDeleted": [],
      "err": [],
    };
    let siteId;

    if (typeof deviceIds == "string" || typeof deviceIds == "number")
      deviceList.push(deviceIds.toString());
    else if (Array.isArray(deviceIds)) deviceList = deviceIds;
    else return res.badRequestWithErrorResponse({ "err": "Insufficient Parameters" });
    let linkedDevices = await getLinkedConfiguratorSystem(deviceIds, "device");
    linkedDevices = [].concat(...linkedDevices);
    if (linkedDevices.length) {
      const deviceIdMapper = linkedDevices.reduce((acc, row) => {
        if (!acc[row.deviceId]) {
          acc[row.deviceId] = [];
        }
        acc[row.deviceId].push(row);
        return acc;
      }, {})
      const messages = Object.entries(deviceIdMapper).map(([deviceId, rows]) => {
        const details = rows.map(row => `${row.systemName} > ${row.subsystemName} > ${row.pageTitle}`).join(', ');
        return `Device(${deviceId}) is linked to Configurator system(s): ${details}`
      })
      const message = messages.join('. ');
      return res.badRequestWithErrorResponse({ err: message });
    }
    emitter.emit("initChange", req);
    let deletedDevArr = [];
    let $didDeletedAllChildFromEnergyMeter = [];
    for (let index in deviceList) {
      let deviceId = deviceList[index];
      try {
        let device = await Devices.findOne({ "deviceId": deviceId });
        siteId = device.siteId;

        if (!device) {
          resObj.notDeleted.push(deviceId);
          resObj.err.push(`DeviceId ${deviceId} Not Present`);
          continue;
        }


        if (device.componentId && device.componentId !== "NA") {
          const componentConfigObj = {
            componentId: device.componentId,
            deviceId: deviceId,
            siteId: siteId,
          }
          const isComponentConfigurationExist = await DeviceService.getComponentConfigurationIfDeviceExist(componentConfigObj, resObj);
          if (isComponentConfigurationExist) {
            continue;
          }
        }


        if (device.deviceType && device.deviceType.toLowerCase() === 'em') {
          sails.log(`Device ${deviceId} is an energy meter`)
          let getChildDevices = await DeviceService.getDevices(siteId)
          getChildDevices = getChildDevices.reduce((acc, deviceData) => {
            if (deviceData.parentEM && deviceData.parentEM === deviceId) {
              acc.push(deviceData);
              return acc;
            }
            return acc;
          }, [])
          if (!_.isEmpty(getChildDevices)) {
            const deleteChildConfig = {
              deviceId,
              siteId
            }
            $didDeletedAllChildFromEnergyMeter = getChildDevices.map((child) => {
              deleteChildConfig.childId = child.deviceId;
              return DeviceService.deleteParentEMFromChildEM(deleteChildConfig, resObj)
            });
            const deletedAllChildFromEnergyMeter = await Promise.all($didDeletedAllChildFromEnergyMeter);

            if (_.includes(deletedAllChildFromEnergyMeter, false)) {
              continue;
            }

          }

          const deleteDeviceConfig = {
            deviceId: deviceId,
            siteId: siteId
          }

          if (device.hasOwnProperty('isMainMeter') && device.isMainMeter.toLowerCase() === 'true') {
            sails.log(`${deviceId} is a part of main energy meter list`);
            const isDeletedFromMainEnergyMeterList = await DeviceService.deleteFromMainMeterList(deleteDeviceConfig, resObj);
            if (!isDeletedFromMainEnergyMeterList) {
              continue;
            }
          }

          const isDeletedFromEnergyMeterList = await DeviceService.deleteFromEnergyMeterList(deleteDeviceConfig, resObj);
          if (!isDeletedFromEnergyMeterList) {
            continue;
          }

        }

        sails.log("Deleted ", device);
        await Devices.destroy({
          "deviceId": deviceId,
          "siteId": siteId,
        });
        deletedDevArr.push(device);
        resObj.deleted.push(deviceId);
      } catch (e) {
        resObj.notDeleted.push(deviceId);
        sails.log.error(e);
      }
    }

    emitter.emit("commitChange", req, "devices", deletedDevArr, []);
    let eventData = {
      "event": "delete",
      "data": resObj.deleted,
    };
    eventService.notifyJouleTrack(siteId, "public", "devices", eventData);
    if (resObj.err.length == 0) resObj.err = null;
    emitter.emit("pushChange", req);
    await deviceService.updateConfigTs({ siteId });
    await siteService.removeSiteCategoriesFromCache(siteId)
    return res.ok(resObj);
  },
  "initController": async function (req, res) {
    let { cntrlId, publish } = req.allParams();
    if (!cntrlId) {
      return res.badRequest();
    }
    try {
      let device = await Devices.findOne({ "deviceId": cntrlId });
      if (!device)
        return res.badRequest({ "err": "Controller Not Configured" });
      if (
        device.deviceType != "joulestat" &&
        device.deviceType != "jouleiocontrol" &&
        device.deviceType != "joulemomo" &&
        device.deviceType != "joulelogger" &&
        device.deviceType != "joulebox"
      ) {
        return res.badRequest({ "err": "Not a controller" });
      }
      let siteId = device.siteId;
      let devicesPromise = Devices.find({ siteId });
      let sitePromise = Sites.findOne({ siteId });
      let deviceTypePromise = DeviceType.find({});
      let componentPromise = Component.find({ "siteId": device.siteId });
      let tsPromise = DyanmoKeyStore.findOne({
        "key": `${device.siteId}_configTS`,
      });
      let devices = await devicesPromise;
      let components = await componentPromise;
      for (let i = 0; i < components.length; i++) {
        let component = components[i];
        helper.convertStringArrayToJSONArray(component.data);
        helper.convertStringArrayToJSONArray(component.controls);
      }
      let params = await deviceTypePromise;
      let site = await sitePromise;
      let self = device;
      let tsData = await tsPromise;
      let timestamp;
      if (typeof tsData != "undefined") timestamp = tsData.value;
      else {
        // timestamp = moment().unix() * 1000;
        await deviceService.updateConfigTs({ siteId });
      }
      if (publish && publish === "true") {
        let payload = {
          "timestamp": timestamp,
          "operation": "verifyConfigVer",
        };
        eventService.publish(`${siteId}/config/all/timestamp`, payload);
      }
      return res.ok({
        components,
        devices,
        params,
        site,
        self,
        timestamp,
      });
    } catch (e) {
      sails.log.error(e);
      return res.serverError();
    }
  },
  /**
   * @function generateControllerConfig
   * @param {Object} req
   * @param {Object} res
   * @param {String} req.param.cntrlId
   * @description Used to get the device configuration of a specific controller.
   * It returns two objects, first is the schema key which will have
   * entries of devices, components, params and self
   * Second key is devConfig which contains device config that is used for L1, L2 and L3 layers to extract data from Sensors and modbus devices.
   * @returns {Object} Object with schema and devconfg keys
   */
  "generateControllerConfig": async function (req, res) {
    let { cntrlId } = req.allParams();
    let devParamMap = {};
    let devConfig = {};
    let devFeedbackMap = {};
    if (!cntrlId) {
      return res.badRequest();
    }
    try {
      let device = await Devices.findOne({ "deviceId": cntrlId });
      if (!device)
        return res.badRequest({ "err": "Controller Not Configured" });
      let { deviceType, siteId } = device;
      let controllerTypes = [
        "joulestat",
        "jouleiocontrol",
        "joulemomo",
        "joulelogger",
        "joulebox",
        "jouleleaf",
        "n3uronbacnetmqtt-controller",
      ];
      if (controllerTypes.indexOf(deviceType) === -1) {
        return res.badRequest({ "err": "Not a controller" });
      }
      // Execution starts here.
      const $component = componentService.getComponents(siteId);
      const $devices = deviceService.getDevices(siteId);
      const $params = parametersService.getParameters(siteId);
      const $ts = DyanmoKeyStore.findOne({
        "key": `${device.siteId}_configTS`,
      });
      const $site = siteService.getSiteInfo(siteId);
      let components = await $component;
      components.map(appendFeedbackPort);
      let deviceArr = await $devices;
      let params = await $params;
      params.map(filterParameters);
      const devices = deviceArr.map(e => e);
      deviceArr
        .filter(
          device =>
            device.controllerId == cntrlId ||
            device.deviceId == cntrlId
        )
        .map(device => {
          const { deviceId } = device;
          if (devParamMap[deviceId])
            device["param"] = devParamMap[deviceId];
          devConfig[deviceId] = device; // Used to send to the IOT.
          return device;
        });
      // Checking for Masibus / Slave controllers
      let connectedSlaveControllers = [];
      for (deviceId in devConfig) {
        let deviceConfiguration = devConfig[deviceId];
        // Finding all slave controllers based on deviceType
        // TODO: Change this to controllerType after adding it to configuration APIs.
        if (deviceConfiguration.isSlaveController);
        connectedSlaveControllers.push(deviceId);
      };
      connectedSlaveControllers.forEach(slaveDeviceId => {
        deviceArr
          .filter(
            device =>
              device.controllerId == slaveDeviceId ||
              device.deviceId == slaveDeviceId
          )
          .map(device => {
            const { deviceId } = device;
            if (devParamMap[deviceId])
              device["param"] = devParamMap[deviceId];
            devConfig[deviceId] = device; // Used to send to the IOT.
            return device;
          });
      });

      let tsData = await $ts;
      let site = await $site;
      let timestamp = Date.now();
      if (typeof tsData != "undefined") {
        timestamp = tsData.value;
      } else {
        await deviceService.updateConfigTs({ siteId });
      }
      return res.ok({
        "schema": {
          components,
          devices,
          "self": device,
          timestamp,
          site,
        },
        devConfig,
      });
    } catch (err) {
      sails.log.error(err);
      return res.serverError();
    }
    /** HELPERS */
    /**
     * @function filterParameters
     * @param {Object} param
     * @description for a given parameter object, function translates into the format required in controllers
     * @returns {Object}
     */
    function filterParameters(param) {
      const {
        filter_oldVal,
        filter_variance,
        filter_existence,
        deviceId,
        abbr,
        utilityType,
        driver,
        max,
        min,
        index,
        mulFactor,
        regType,
        offset,
        bitIndex,
        secDriver,
      } = param;
      const properties = {
        driver,
        max,
        min,
        index,
        regType,
        mulFactor,
        offset,
        bitIndex,
        secDriver
      };
      const filter = {
        "oldVal": filter_oldVal,
        "variance": filter_variance,
        "existence": filter_existence,
      };
      param["filter"] = filter;
      param["properties"] = properties;
      if (param.hasOwnProperty('decimalPrecision')) {
        param["decimalPrecision"] = Number.parseInt(param["decimalPrecision"]);
      }
      if (utilityType === "command") {
        const key = `${deviceId}_${abbr}`;
        if (devFeedbackMap[key]) {
          param["feedbackPort"] = devFeedbackMap[key];
        }
      }
      delete param.driver;
      delete param.max;
      delete param.min;
      delete param.index;
      delete param.mulFactor;
      delete param.offset;
      delete param.bitIndex;
      delete param.deviceId_abbr;
      delete param.filter_existence;
      delete param.filter_oldVal;
      delete param.filter_variance;
      delete param.siteId;
      delete param.abbr;
      delete param.secDriver;
      if (!devParamMap[deviceId]) devParamMap[deviceId] = {};
      devParamMap[deviceId][abbr] = param;
    }
    function appendFeedbackPort(component) {
      let { controls } = component;
      if (!controls) return;
      for (let controlPoint of controls) {
        const { deviceId, min, max, unit, timeout, key, device_abbr } = controlPoint;
        let { expression } = controlPoint;
        const feedbackPortConfig = {
          expression,
          min,
          max,
          unit,
          timeout,
        };
        let feedbackDevices = helper.parseFormulaToIds(expression);
        feedbackPortConfig["deviceId"] = feedbackDevices;
        //Joulerecipe uses . notation eg <deviceId>.<param> instead of
        // deviceId@<param> so the code below traslates it.
        expression = expression.replace(/\|\|/g, " ");
        expression = expression.substring(1, expression.length - 1);
        feedbackPortConfig["expression"] = expression.replace(
          /@/g,
          "."
        );
        if (device_abbr)
          devFeedbackMap[`${deviceId}_${device_abbr}`] = feedbackPortConfig;
        else
          devFeedbackMap[`${deviceId}_${key}`] = feedbackPortConfig;
      }
    }
  },
  "insertCurrentVersion": async (req, res) => {
    let siteId = req.body.siteId;
    let currentVersion = req.body.currentVersion;
    let deviceIdList = [];
    try {
      let data = await Devices.find().where({
        "siteId": siteId,
      });
      for (let i in data) {
        deviceIdList.push(data[i].deviceId);
      }
    } catch (error) {
      sails.log(error);
    }
    // var createStream = fs.createWriteStream("./JournalDEV.json");
    // fs.writeFile('./JournalDEV.json', JSON.stringify(data) , 'utf-8');
    // createStream.end();

    for (let j in deviceIdList) {
      let deviceId = deviceIdList[j];
      await Devices.update({
        "siteId": siteId,
        "deviceId": deviceId,
      }).set({
        "currentVersion": currentVersion,
      });
    }
    res.send(200);
  },
};

const { toJson } = require("../../utils/globalhelper");
const iotCoreService = require("../../services/iotCore/iotCore.public");
const deviceService = require("../../services/device/device.service");
const auditEventLogService = require("../../services/auditEventLog/auditEventLog.public");
const moment = require("moment");

module.exports = {
  friendlyName: "change-mode-of-device-control",
  description: "change-mode-of-device-control",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: {
        id: "<EMAIL>",
        _role: "admin",
        _site: "suh-hyd",
      },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
      required: true,
    },
    did: {
      type: "json",
      required: true,
    },
    componentId: {
      type: "string",
      required: true,
    },
    controllerId: {
      type: "string",
      required: true,
    },
  },

  exits: {
    serverError: {
      responseType: "serverError",
      description: "[device -> mode-change-device] Server Error!",
    },
    badRequest: {
      statusCode: 400,
      responseType: "badRequest",
      description: "[device -> mode-change-device] Bad Request!",
    },
    success: {
      statusCode: 200,
      description: "[device -> mode-change-device] Mode change request published successfully",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const {
        siteId,
        did,
        controllerId,
        componentId,
        _userMeta: { id: userId },
      } = inputs;

      const transactionId = this.req?.headers?.["x-transaction-id"];
      const didArray = toJson(did);

      sails.log.info(
        `level="INFO" app="jt-api-v2" operation="modeChange" step="1/8" requestId="${transactionId}" userId="${userId}" siteId="${siteId}" componentId="${componentId}" modeChangeDetail="${JSON.stringify(didArray)}" controllerId="${controllerId}" message="Mode Change request initiated" state="in-progress"`,
      );

      for (const deviceDotParam in didArray) {
        if (deviceDotParam.split(".").length !== 2) {
          return exits.badRequest("Invalid device.param format in 'did'");
        }
      }

      const masterControllerId =
        await deviceService.getMasterControllerByControllerId(controllerId);

      const payload = {
        operation: "InsertInModes",
        componentId,
        requestId: transactionId,
        userId,
        timestamp: moment().toISOString(),
        extra: {
          controllerId: masterControllerId,
          siteId,
        },
        config: { ...didArray },
      };

      const topic = `${siteId}/config/${masterControllerId}/mode`;
      await iotCoreService.publish(topic, JSON.stringify(payload));
      sails.log.info(
        `level="INFO" app="jt-api-v2" operation="modeChange" step="2/8" requestId="${transactionId}" userId="${userId}" siteId="${siteId}" componentId="${componentId}" modeChangeDetail="${JSON.stringify(didArray)}" controllerId="${controllerId}" message="Mode Change request published to IoT Core" state="in-progress"`,
      );

      const auditPayload = {
        event_name: "state_update_mode_change",
        user_id: userId,
        site_id: siteId,
        asset_id: componentId,
        req: this.req,
        prev_state: null,
        curr_state: Object.assign({}, { ...payload, topic }),
      };

      auditEventLogService.emit(auditPayload);

      sails.log.info(
        `level="INFO" app="jt-api-v2" operation="modeChange" step="3/8" requestId="${transactionId}" userId="${userId}" siteId="${siteId}" componentId="${componentId}" modeChangeDetail="${JSON.stringify(didArray)}" controllerId="${controllerId}" masterControllerId="${masterControllerId}" message="Mode change request placed successfully" state="in-progress"`,
      );
      return exits.success({
        payload,
        topic,
        message: "Mode change request placed successfully",
      });
    } catch (err) {
      sails.log.error("[device -> mode-change-device] Error:");
      sails.log.error(err);
      switch (err.code) {
        case "E_INVALID_CONTROLLER_ID":
          return exits.badRequest({ err: err.message });

        case "E_CONTROLLER_CATEGORY_MISSING":
          return exits.badRequest({ err: err.message });

        case "E_MASTER_CONTROLLER_MISSING":
          return exits.badRequest({ err: err.message });

        case "E_MASTER_CONTROLLER_NOT_FOUND":
          return exits.badRequest({ err: err.message });

        default:
          return exits.serverError(err);
      }
    }
  },
};

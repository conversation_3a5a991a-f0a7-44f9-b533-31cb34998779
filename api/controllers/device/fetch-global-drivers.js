const devicetypeService = require('../../services/devicetype/devicetype.service');

module.exports = {

  friendlyName: 'Fetch global drivers',
  description: 'Fetch global drivers.',
  example: [
    `curl -X GET /m2/device/v1/global-drivers`,
  ],
  inputs: {

  },

  exits: {
    success:{
        description: 'Global drivers fetched successfully.',
        statusCode: 200
      },
    serverError: {
        description: 'Server error occurred while fetching global drivers.',
        statusCode: 500
    }
  },

  fn: async function (inputs, exits) {

    try {
      const globalDrivers = await devicetypeService.fetchGlobalDrivers()
      return exits.success(globalDrivers);
    } catch (e) {
      sails.log.error('[error > fetch-global-drivers]',e);
      return exits.serverError(e);
    }

  }
};


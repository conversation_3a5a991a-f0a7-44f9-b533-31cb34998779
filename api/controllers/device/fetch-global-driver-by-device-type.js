const devicetypeService = require('../../services/devicetype/devicetype.service');

module.exports = {

  friendlyName: 'Fetch global driver by device type',
  description: 'Fetch global driver by device type',
  example: [
    `curl -X GET/m2/device/v2/driver/global/:deviceType`,
  ],
  inputs: {
    deviceType: {
      type: 'string',
      description: 'Filter by device type, if not provided, all global drivers will be fetched.'
    }
  },

  exits: {
    badRequest: {
      description: 'Bad request',
      statusCode: 400
    },
    success: {
      description: 'Global driver fetched successfully.',
      statusCode: 200
    },
  },

  fn: async function ({deviceType}, exits) {
    try {
      if (_.isEmpty(deviceType)) {
        return exits.badRequest({err:'Device type is required.'});
      }
      const globalDriver = await devicetypeService.fetchGlobalDriverByDeviceType(deviceType)
      if (!globalDriver) {
        return exits.badRequest({err:`No global driver found for device type: ${deviceType}`});
      }
      return exits.success(globalDriver);
    } catch (e) {
      sails.log.error('[error > fetch-global-driver-by-deviceType]',e);
      return exits.serverError(e);
    }

  }
};


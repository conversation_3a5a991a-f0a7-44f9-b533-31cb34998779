const dynamokeystorePublic = require("../../services/dynamokeystore/dynamokeystore.public");

module.exports = {
  friendlyName: "Fetch parameter units",
  description: "fetch units for parameters.",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
    siteId: {
      type: "string",
      required: true,
      description: "Site id to fetch the bacnet devices",
    },
  },

  exits: {
    success: {
      description: "Success",
    },
    serverError: {
      statusCode: 500,
      description: "Server error occurred",
    },
    notFound: {
      statusCode: 404,
      description: "Devices not found.",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad request",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId } = inputs;

      const keyName = "parameterUnitGroupMapping";
      const parameterUnitGroupMappingRecord = await dynamokeystorePublic.findOne({ key: keyName });

      if (!parameterUnitGroupMappingRecord) {
        return exits.notFound({ error: `Key ${keyName} not found in dynamoKeyStore.` });
      }

      const parameterUnitGroupMapping = parameterUnitGroupMappingRecord.value;
      let parsedParameterUnitGroupMapping;
      try {
        parsedParameterUnitGroupMapping = JSON.parse(parameterUnitGroupMapping);
      } catch (e) {
        sails.log.error(`Failed to parse parameterUnitGroupMapping JSON: ${e.message}`);
        return exits.serverError({ error: "Failed to parse parameter unit group mapping." });
      }
      return exits.success({ parameterUnitGroupMapping: parsedParameterUnitGroupMapping });
    } catch (error) {
      sails.log.error(`Error in fetchBacnetDevice: ${error}`);

      switch (error.code) {
        case "E_NOT_FOUND": {
          return exits.notFound({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  },
};

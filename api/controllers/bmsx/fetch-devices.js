const deviceService = require("../../services/device/device.public");
const parameterService = require("../../services/parameter/parameter.private");
const deviceTypeService = require("../../services/devicetype/devicetype.service");


module.exports = {
  friendlyName: "Fetch dejoule devices.",
  description: "fetch dejoule devices.",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
    siteId: {
      type: "string",
      required: true,
      description: "Site id to fetch the bacnet devices",
    },
    controllerId: {
      type: "string",
      required: true,
      description: "controller id of the bacnet slave controller",
      example: "33280",
    },
  },

  exits: {
    success: {
      description: "Success",
    },
    serverError: {
      statusCode: 500,
      description: "Server error occurred",
    },
    notFound: {
      statusCode: 404,
      description: "Devices not found.",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad request",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, controllerId } = inputs;

      //query devices table from dynamo using site id and controller id for 3rd party master controller
      const devices = await deviceService.find({
        siteId,
        controllerId,
      });

      // no devices are found
      if (!devices || devices.length === 0) {
        return exits.success([]);
      }

      // Map parameters for all devices
      const devicesWithParameters = await Promise.all(
        devices.map(async (device) => {
          try {
            // Find parameters for this device using findWithoutAbbr
            let parameters = await parameterService.findWithoutAbbr(siteId, device.deviceId);

            let unmappedParameters = await deviceTypeService.fetchGlobalDriverByDeviceType(
              device.deviceType
            );

            if (
              unmappedParameters &&
              unmappedParameters.parameters &&
              Array.isArray(unmappedParameters.parameters)
            ) {
              // Filter out parameters that don't exist in the parameters array
              unmappedParameters.parameters = unmappedParameters.parameters.filter((param) => {
                return (
                  param.abbr &&
                  !parameters.some((existingParam) => existingParam.abbr === param.abbr)
                );
              });
            }

            // Merge parameters and unmappedParameters, with unmappedParameters at the end
            const mergedParameters = [
              ...(parameters || []),
              ...(unmappedParameters && unmappedParameters.parameters
                ? unmappedParameters.parameters
                : []),
            ];

            console.log("neh - unmapped params ", device.deviceId, unmappedParameters.parameters);

            // Add parameters to device object
            return {
              ...device,
              parameters: mergedParameters || [],
            };
          } catch (error) {
            sails.log.error(`Error fetching parameters for device ${device.deviceId}: ${error}`);
            // Return device without parameters if there's an error
            return {
              ...device,
              parameters: [],
            };
          }
        })
      );

      return exits.success({ devices: devicesWithParameters });
    } catch (error) {
      sails.log.error(`Error in fetchDejouleDevices: ${error}`);

      switch (error.code) {
        case "E_NOT_FOUND": {
          return exits.notFound({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  },
};

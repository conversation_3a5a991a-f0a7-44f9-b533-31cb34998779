const deviceService = require("../../services/device/device.public");
const _ = require("lodash");

module.exports = {
  friendlyName: "Fetch bacnet devices",
  description: "fetch bacnet devices as third party master controllers.",

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
    siteId: {
      type: "string",
      required: true,
      description: "Site id to fetch the bacnet devices",
    },
    secondaryControllerId: {
      type: "number",
      required: true,
      description: "controller id of the bacnet slave controller",
    },
  },

  exits: {
    success: {
      description: "Success",
    },
    serverError: {
      statusCode: 500,
      description: "Server error occurred",
    },
    notFound: {
      statusCode: 404,
      description: "Devices not found.",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad request",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, secondaryControllerId } = inputs;

      //query devices table from dynamo using site id and secondary controller id
      // The 'select' projection is not working for this DynamoDB query.
      // Manually picking the required fields after fetching.
      const foundDevices = await deviceService.find({
        where: {
          siteId,
          secondaryControllerId,
        },
      });

      const devices = foundDevices.map(
        ({ deviceId, siteId, name, deviceType, secondaryControllerId }) => ({
          deviceId,
          siteId,
          name,
          deviceType,
          secondaryControllerId,
        })
      );

      // no devices are found
      if (!devices || devices.length === 0) {
        return exits.notFound({
          err: `No BACnet devices found for the given site and slave controller.`,
        });
      }

      // Create a list of device IDs from the fetched devices.
      const deviceIds = devices.map((device) => device.deviceId);

      // Query the BMSDevices model to get additional details for these devices.
      const bmsDevices = await BMSDevice.find({
        where: {
          refDeviceId: deviceIds,
        },
        select: ["name", "status", "refDeviceId"],
      });

      // Create a map of BMS devices, keyed by their refDeviceId for efficient lookup.
      const bmsDeviceMap = _.keyBy(bmsDevices, "refDeviceId");

      // Augment the original device list with the BMS device details.
      const mappedDevices = devices.map((device) => ({
        ...device,
        bmsConfig: ((bmsDevice) =>
          bmsDevice ? { name: bmsDevice.name, status: bmsDevice.status } : null)(
          bmsDeviceMap[device.deviceId]
        ),
      }));

      return exits.success({ devices: mappedDevices });
    } catch (error) {
      sails.log.error(`Error in fetchBacnetDevice: ${error}`);

      switch (error.code) {
        case "E_NOT_FOUND": {
          return exits.notFound({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  },
};

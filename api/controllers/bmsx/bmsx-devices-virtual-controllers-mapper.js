const bmsxService = require('../../services/bmsx/bmsx.service');

/**
 * Configure discovered devices - handles both newly discovered devices and existing third-party master controller location updates
 *
 * Newly discovered devices = Discovered devices that will be configured as third-party master controllers
 * Existing devices = Third-party master controllers that need location updates
 */
module.exports = {
  friendlyName: 'Configure Discovered Devices',
  description: 'Configure newly discovered devices as third-party master controllers and update existing device locations',

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes'
    },

    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID where devices should be configured'
    },

    slaveControllerId: {
      type: 'string',
      required: true,
      description: 'Slave controller ID (deviceId in DynamoDB)'
    },

    deviceType: {
      type: 'string',
      required: true,
      description: 'Device type for all devices being configured (e.g., "n3uronbacnetmqtt-controller")'
    },

    vendorId: {
      type: 'string',
      required: true,
      description: 'Vendor ID for all devices being configured (e.g., "n3uronbacnetmqtt")'
    },

    devices: {
      type: 'ref',
      required: true,
      description: 'Array of discovered devices to configure with their location assignments',
      example: [
        {
          discoveredDeviceId: "1",
          controllerId: null, // null for new devices, deviceId for existing
          name: "AHU-01", // device name from deviceProperties
          location: {
            areaId: "roof-area",      // required for non-IBMS, ignored for IBMS
            regionId: "hvac-region",  // required for non-IBMS, ignored for IBMS
            leafNodeId: null          // required for IBMS, ignored for non-IBMS
          }
        }
      ]
    }
  },

  exits: {
    success: {
      statusCode: 200,
    },
    badRequest: {
      statusCode: 400,
    },
    notFound: {
      statusCode: 404,
    },
    serverError: {
      responseType: "serverError",
      statusCode: 500,
    },
  },

  fn: async function (inputs, exits) {
    const { siteId, slaveControllerId, deviceType, vendorId, devices } = inputs;

    try {
      // Validate inputs
      if (!devices || !Array.isArray(devices) || devices.length === 0) {
        throw new Error('Devices array is required and must not be empty');
      }

      // Check BMS connector discovery status
      const bmsConnectorDetail = await BMSConnector.findOne({
        siteId: siteId,
        slaveControllerId: parseInt(slaveControllerId),
        status: 1
      });

      if (!bmsConnectorDetail) {
        return exits.notFound({
          message: "BMS connector not found for the given slave controller",
        });
      }

      if (bmsConnectorDetail.discoveryStatus === 1) {
        return exits.badRequest({
          message: 'Cannot save device configuration while discovery is in progress. Please wait for discovery to complete.'
        });
      }

      const results = await bmsxService.configureDiscoveredDevices(siteId, slaveControllerId, deviceType, vendorId, devices);

      // Calculate total DeJoule devices updated
      const dejouleDevicesUpdated = results.masterControllersUpdated.reduce((total, device) => {
        return total + (device.dejouleDevicesUpdated || 0);
      }, 0);

      const response = {
        masterControllersCreated: results.masterControllersCreated,
        masterControllersUpdated: results.masterControllersUpdated,
        dejouleDevicesUpdated: dejouleDevicesUpdated,
        errors: results.errors || []
      };

      sails.log.info(`Discovered device configuration completed: ${results.masterControllersCreated.length} master controllers created, ${results.masterControllersUpdated.length} master controllers updated, ${dejouleDevicesUpdated} DeJoule devices updated, ${results.errors.length} errors`);

      return exits.success(response);

    } catch (error) {
      sails.log.error('[BMS > configure-devices] Error:', error);

      if (error.code === 'E_NOT_FOUND') {
        return exits.notFound({ message: error.message });
      }

      if (error.code === 'E_BAD_REQUEST') {
        return exits.badRequest({ message: error.message });
      }

      return exits.serverError({
        message: 'Failed to configure discovered devices',
        error: error.message
      });
    }
  }
};

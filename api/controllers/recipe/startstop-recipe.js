
const recipeService = require('../../services/recipe/recipe.service');
const iotCoreService = require("../../services/iotCore/iotCore.public");

module.exports = {
  friendlyName: 'startstopRecipe',
  description: 'Staging a logic means just transferring it to controller/server. This route is when a logic is already stage, to actually start/stop running it',
  example: [
    `curl -X GET "http://localhost:1337/`,
  ],

  inputs: {
    siteId: {
      type: 'string',
      example: 'ssh',
      required: true
    },
    rid: {
      type: 'string',
      exmaple: '04772c0d-303a-498f-86bd-3568a4fe41ea',
      required: true
    },
    startStop: {
      type: 'boolean',
      example: true,
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > startstopRecipe] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > startstopRecipe] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > startstopRecipe] forbidden Request!',
    },
  },

  fn: async function (inputs, exits) {

    /**
     * Initially when a recipe is deployed it has 2 deciding values that tells recipe is deployed and running
     * isStage(Initially : 1 means recipe is only in db nowhere else)
     * switchOff (Initially : 0 means run recipe )
     * switchoff can we used when a recipe has isStage 0, to stop it from running
     */

    try {
      let { rid, siteId, startStop } = inputs;
      let status = startStop ? '0' : '1'; // 0 means start the recipe, 1 means stop the recipe
      let recipe = await recipeService.findOne({ rid, siteId });
      let runOn;

      if (!recipe) {
        return exits.forbidden({ problems: ['Recipe doesn\'t exist'] });
      }
      if (recipe['isStage'] === '1') {
        return exits.badRequest({ problems: ['Recipe is not staged'] });
      }

      runOn = recipe['runOn'];

      if (runOn === 'server') {
        await recipeService.update({ rid, siteId }, { 'switchOff': status });
      } else {
        let updateObject = {
          func: 'startStopRecipe',
          operation: 'recipeControl',
          data: [{
            rid,
            switchOff: status, // status = 1 means stop the recipe, 0 means start the recipe.
          }]
        };
        let topic = `${siteId}/config/${runOn}/recipecontrol`;
        await iotCoreService.publish(topic, JSON.stringify(updateObject));
      }
      return exits.success({'status': 'in-process'});

    } catch (error) {
      sails.log.error('[recipe > startstopRecipe] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  }
};

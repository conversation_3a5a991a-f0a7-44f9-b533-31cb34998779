const siteService = require('../../services/site/site.service');

module.exports = {
  friendlyName: 'getSite',
  description: 'Fetching site info for one site..',
  example: [
    'curl -X GET "http://localhost:1337/site/v2/site/:key/:value',
  ],

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes',
    },
    filter: {
      type: 'json',
      example: 'All the applicable filters',
    },
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[recipe > getSite] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[recipe > getSite] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[recipe > getSite] forbidden Request!',
    },
  },

  async fn(inputs, exits) {
    try {
      const {
        _userMeta: { _site: siteId },
      } = inputs;
      const { filter } = inputs;
      let query = {};

      query.siteId = siteId;
      query = { ...query, ...filter };
      const site = await siteService.findOne(query);

      return exits.success(site);
    } catch (error) {
      sails.log.error('[site > getSite] Error!');
      sails.log.error(error);
      exits.serverError(error);
    }
  },
};

const siteService = require('../../services/site/site.service');
const globalHelper = require('../../utils/globalhelper');
const processService = require('../../services/process/process.service');

module.exports = {
  friendlyName: 'test',
  description : 'Test function for testing purposes only.',
  example: [
    `curl -X POST "http://localhost:1337/test`,
  ],

  inputs: {
    testObj: {
      type: 'json',
      example: 'Id of site',
      custom: function(testObj){
        let errors = globalHelper.checkObjectKeys(testObj,[
          'areas'
        ], 'testObj');
        if(errors.length === 0) return true;
        else throw new Error(errors);
      }
    },
    query: {
      type: 'json',
    },
    tableName: {
      type: 'string'
    },
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: 'site > testFunction Server Error.',
    },
  },

  fn: async function (inputs, exits) {
    try{
      let { query, tableName } = inputs;
      console.log(sails);
      let data = await siteService.find({siteId:'tms-del'});
      // console.log(data);
      exits.success(data);
    } catch(error) {
      console.log('[site > getSite] Error!');
      console.log(error);
      exits.serverError(error);
    }
  }
};
